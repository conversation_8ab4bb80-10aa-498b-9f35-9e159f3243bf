# envoy-transparent-multiport.yaml
static_resources:
  listeners:
  # Listener for Transparent HTTP Proxy (Port 15001)
  - name: http_transparent_listener
    address:
      socket_address:
        protocol: TCP
        address: 0.0.0.0
        port_value: 15001 # iptables redirects 80 -> 15001
    listener_filters:
    - name: "envoy.filters.listener.original_dst" # Capture original destination
      typed_config:
        "@type": type.googleapis.com/envoy.extensions.filters.listener.original_dst.v3.OriginalDst
    filter_chains:
    - filters:
      - name: envoy.filters.network.http_connection_manager
        typed_config:
          "@type": type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
          stat_prefix: http_transparent
          codec_type: AUTO
          route_config:
            name: local_route
            virtual_hosts:
            - name: local_service
              domains: ["*"]
              routes:
              - match:
                  prefix: "/"
                route:
                  cluster: dynamic_forward_cluster
          http_filters:
          - name: envoy.filters.http.lua
            typed_config:
              "@type": type.googleapis.com/envoy.extensions.filters.http.lua.v3.Lua
              inline_code: |
                function envoy_on_request(request_handle)
                  local host = request_handle:headers():get(":authority")
                  if not host then
                    host = request_handle:headers():get("host")
                  end

                  if host then
                    -- Define allowed domains for HTTP traffic
                    local allowed_domains = {
                      "example.com",
                      "www.example.com",
                      "api.example.com",
                      "httpbin.org",
                      "www.httpbin.org"
                    }

                    local allowed = false
                    for _, domain in ipairs(allowed_domains) do
                      if host == domain or string.match(host, "%." .. domain:gsub("%.", "%%.") .. "$") then
                        allowed = true
                        break
                      end
                    end

                    if allowed then
                      request_handle:logInfo("Allowed HTTP request to: " .. host)
                    else
                      request_handle:logWarn("Blocked HTTP request to: " .. host)
                      request_handle:respond({[":status"] = "403"}, "Access to " .. host .. " is not allowed")
                      return
                    end
                  else
                    request_handle:logWarn("Blocked HTTP request: No host header")
                    request_handle:respond({[":status"] = "400"}, "Bad Request: No host header")
                    return
                  end
                end
          - name: envoy.filters.http.router
            typed_config:
              "@type": type.googleapis.com/envoy.extensions.filters.http.router.v3.Router

  # Listener for Transparent HTTPS/TLS Passthrough (Port 15002)
  - name: https_transparent_listener
    address:
      socket_address:
        protocol: TCP
        address: 0.0.0.0
        port_value: 15002 # iptables redirects 443 -> 15002
    listener_filters:
    - name: "envoy.filters.listener.original_dst" # Capture original destination
      typed_config:
        "@type": type.googleapis.com/envoy.extensions.filters.listener.original_dst.v3.OriginalDst
    - name: "envoy.filters.listener.tls_inspector" # Crucial for SNI extraction
      typed_config:
        "@type": type.googleapis.com/envoy.extensions.filters.listener.tls_inspector.v3.TlsInspector
    filter_chains:
    - filter_chain_match:
        transport_protocol: "tls" # This chain ONLY handles connections detected as TLS
      filters:
      # Lua filter at the network level for TLS passthrough
      - name: envoy.filters.network.lua
        typed_config:
          "@type": type.googleapis.com/envoy.extensions.filters.network.lua.v3.Lua
          inline_code: |
            function envoy_on_new_connection(connection)
              local sni_host = connection:streamInfo():dynamicMetadata():get("envoy.filters.listener.tls_inspector"):get("sni_hostname")

              if sni_host then
                -- Define allowed domains for HTTPS traffic
                local allowed_domains = {
                  "example.com",
                  "www.example.com",
                  "api.example.com",
                  "httpbin.org",
                  "www.httpbin.org",
                  "google.com",
                  "www.google.com"
                }

                local allowed = false
                for _, domain in ipairs(allowed_domains) do
                  if sni_host == domain or string.match(sni_host, "%." .. domain:gsub("%.", "%%.") .. "$") then
                    allowed = true
                    break
                  end
                end

                if allowed then
                  connection:logInfo("Allowed HTTPS SNI: " .. sni_host)
                  return nil -- Allow the connection to proceed
                else
                  connection:logWarn("Blocked HTTPS SNI: " .. sni_host)
                  connection:close()
                  return "stop"
                end
              else
                connection:logWarn("Blocked HTTPS: No SNI detected.")
                connection:close()
                return "stop"
              end
            end
      - name: envoy.filters.network.tcp_proxy # Forward the raw TCP stream
        typed_config:
          "@type": type.googleapis.com/envoy.extensions.filters.network.tcp_proxy.v3.TcpProxy
          stat_prefix: https_passthrough
          cluster: dynamic_forward_cluster

  clusters:
  - name: dynamic_forward_cluster
    connect_timeout: 10s
    type: ORIGINAL_DST # Crucial for transparent proxying (uses IP from iptables)
    lb_policy: CLUSTER_PROVIDED
    # No transport_socket here as Envoy is not originating TLS for the client's session.
    # It's either plain HTTP or raw TLS tunnel.
    # For HTTPS traffic, the original client's TLS handshake continues directly to the upstream.
    typed_extension_protocol_options:
      envoy.extensions.upstreams.http.v3.HttpProtocolOptions:
        "@type": type.googleapis.com/envoy.extensions.upstreams.http.v3.HttpProtocolOptions
        explicit_http_config:
          http2_protocol_options: {} # Enable HTTP/2 for upstream if clients use it

admin:
  access_log_path: "/tmp/admin_access.log"
  address:
    socket_address:
      protocol: TCP
      address: 127.0.0.1
      port_value: 9901