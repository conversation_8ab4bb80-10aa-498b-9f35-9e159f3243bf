# envoy-transparent-multiport.yaml
static_resources:
  listeners:
  # Listener for Transparent HTTPS/TLS Passthrough (Port 15002)
  - name: https_transparent_listener
    address:
      socket_address:
        protocol: TCP
        address: 0.0.0.0
        port_value: 15002 # iptables redirects 443 -> 15002
    listener_filters:
    - name: "envoy.filters.listener.original_dst" # Capture original destination
      typed_config:
        "@type": type.googleapis.com/envoy.extensions.filters.listener.original_dst.v3.OriginalDst
    - name: "envoy.filters.listener.tls_inspector" # Crucial for SNI extraction
      typed_config:
        "@type": type.googleapis.com/envoy.extensions.filters.listener.tls_inspector.v3.TlsInspector
    filter_chains:
    - filter_chain_match:
        transport_protocol: "tls" # This chain ONLY handles connections detected as TLS
    filters:
    # Lua filter at the network level for TLS passthrough
    - name: envoy.filters.network.lua # Use the correct filter name
      typed_config:
        "@type": type.googleapis.com/envoy.extensions.filters.network.lua.v3.Lua # CORRECTED TYPE URL
        inline_code: |
          function envoy_on_new_connection(connection)
            local sni_host = connection:streamInfo():dynamicMetadata():get("envoy.filters.listener.tls_inspector"):get("sni_hostname")

            if sni_host then
              -- Simple example: allow example.com and anything else starting with www.
              -- For everything else, deny.
              if string.match(sni_host, "example.com$") or string.match(sni_host, "^www%.") then
                connection:logDebug("Allowed HTTPS SNI: " .. sni_host)
                -- If allowed, return nil to allow the next filter (tcp_proxy) to proceed
                return nil
              else
                connection:logWarn("Blocked HTTPS SNI: " .. sni_host)
                -- If blocked, close the connection to deny access
                connection:close()
                return "stop" -- Stop processing this connection
              end
            else
              connection:logWarn("Blocked HTTPS: No SNI detected.")
              connection:close()
              return "stop"
            end
          end
    - name: envoy.filters.network.tcp_proxy # Forward the raw TCP stream
      typed_config:
        "@type": type.googleapis.com/envoy.extensions.filters.network.tcp_proxy.v3.TcpProxy
        stat_prefix: https_passthrough
        cluster: dynamic_forward_cluster
  # Listener for Transparent HTTPS/TLS Passthrough (Port 15002)
  - name: https_transparent_listener
    address:
      socket_address:
        protocol: TCP
        address: 0.0.0.0
        port_value: 15002 # iptables redirects 443 -> 15002
    listener_filters:
    - name: "envoy.filters.listener.original_dst" # Capture original destination
      typed_config:
        "@type": type.googleapis.com/envoy.extensions.filters.listener.original_dst.v3.OriginalDst
    - name: "envoy.filters.listener.tls_inspector" # Crucial for SNI extraction
      typed_config:
        "@type": type.googleapis.com/envoy.extensions.filters.listener.tls_inspector.v3.TlsInspector
    filter_chains:
    - filter_chain_match:
        transport_protocol: "tls" # This chain ONLY handles connections detected as TLS
    - filters:
      # Lua filter at the network level for TLS passthrough
      - name: envoy.filters.network.lua
        typed_config:
          "@type": type.googleapis.com/envoy.extensions.filters.network.lua.v3.Lua
          inline_code: |
            function envoy_on_new_connection(connection)
              local sni_host = connection:streamInfo():dynamicMetadata():get("envoy.filters.listener.tls_inspector"):get("sni_hostname")

              if sni_host then
                -- Simple example: allow example.com and anything else starting with www.
                -- For everything else, deny.
                if string.match(sni_host, "example.com$") or string.match(sni_host, "^www%.") then
                  connection:logDebug("Allowed HTTPS SNI: " .. sni_host)
                  -- If allowed, return nil to allow the next filter (tcp_proxy) to proceed
                  return nil
                else
                  connection:logWarn("Blocked HTTPS SNI: " .. sni_host)
                  -- If blocked, close the connection to deny access
                  connection:close()
                  return "stop" -- Stop processing this connection
                end
              else
                connection:logWarn("Blocked HTTPS: No SNI detected.")
                connection:close()
                return "stop"
              end
            end
      - name: envoy.filters.network.tcp_proxy # Forward the raw TCP stream
        typed_config:
          "@type": type.googleapis.com/envoy.extensions.filters.network.tcp_proxy.v3.TcpProxy
          stat_prefix: https_passthrough
          cluster: dynamic_forward_cluster # Use the original destination from iptables
          # No upstream TLS context needed here, as Envoy is just relaying bytes.

  clusters:
  - name: dynamic_forward_cluster
    connect_timeout: 10s
    type: ORIGINAL_DST # Crucial for transparent proxying (uses IP from iptables)
    lb_policy: CLUSTER_PROVIDED
    # No transport_socket here as Envoy is not originating TLS for the client's session.
    # It's either plain HTTP or raw TLS tunnel.
    # For HTTPS traffic, the original client's TLS handshake continues directly to the upstream.
    typed_extension_protocol_options:
      envoy.extensions.upstreams.http.v3.HttpProtocolOptions:
        "@type": type.googleapis.com/envoy.extensions.upstreams.http.v3.HttpProtocolOptions
        explicit_http_config:
          http2_protocol_options: {} # Enable HTTP/2 for upstream if clients use it

admin:
  access_log_path: "/tmp/admin_access.log"
  address:
    socket_address:
      protocol: TCP
      address: 127.0.0.1
      port_value: 9901